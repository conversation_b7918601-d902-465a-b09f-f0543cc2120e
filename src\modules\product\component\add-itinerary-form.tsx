'use client'

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus } from 'lucide-react'

// Define the props interface
interface AddItineraryFormProps {
  onAddItinerary: (item: any) => void;
}

export function AddItineraryForm({ onAddItinerary }: AddItineraryFormProps) {
  // State for each form field
  const [heading, setHeading] = useState('');
  const [dayNumber, setDayNumber] = useState('');
  const [title, setTitle] = useState('');
  const [trekDistance, setTrekDistance] = useState('');
  const [flightHours, setFlightHours] = useState('');
  const [drivingHour, setDrivingHour] = useState('');
  const [highestAltitude, setHighestAltitude] = useState('');
  const [trekDuration, setTrekDuration] = useState('');
  const [image, setImage] = useState<File | null>(null);
  const [details, setDetails] = useState(''); // Added details field

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault(); // Prevent page reload

    const newItem = {
      heading,
      day: dayNumber,
      title,
      trekDistance,
      flightHours,
      drivingHour,
      highestAltitude,
      trekDuration,
      details,
      // Create a temporary URL for the image to display it.
      // In a real app, you would upload the file to a server and use the returned URL.
      image: image ? URL.createObjectURL(image) : undefined,
    };

    onAddItinerary(newItem);

    // Optional: Reset form fields after submission
    setHeading('');
    setDayNumber('');
    setTitle('');
    setTrekDistance('');
    setFlightHours('');
    setDrivingHour('');
    setHighestAltitude('');
    setTrekDuration('');
    setDetails('');
    setImage(null);
    // You might need to clear the file input differently
    const fileInput = document.getElementById('image') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Plus className="w-5 h-5" />
          Add Package Itinerary
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="heading">Heading</Label>
            <Input id="heading" value={heading} onChange={(e) => setHeading(e.target.value)} placeholder="Enter Heading" />
          </div>
          <div>
            <Label htmlFor="day-number">Day Number</Label>
            <Input id="day-number" value={dayNumber} onChange={(e) => setDayNumber(e.target.value)} placeholder="Enter Day Number" />
          </div>
          <div>
            <Label htmlFor="title">Title</Label>
            <Input id="title" value={title} onChange={(e) => setTitle(e.target.value)} placeholder="Enter Title" />
          </div>
          <div>
            <Label htmlFor="details">Activity Details</Label>
            <Input id="details" value={details} onChange={(e) => setDetails(e.target.value)} placeholder="Enter Details" />
          </div>
          <div>
            <Label htmlFor="trek-distance">Trek Distance</Label>
            <Input id="trek-distance" value={trekDistance} onChange={(e) => setTrekDistance(e.target.value)} placeholder="e.g., 5km" />
          </div>
          <div>
            <Label htmlFor="flight-hours">Flight Hours</Label>
            <Input id="flight-hours" value={flightHours} onChange={(e) => setFlightHours(e.target.value)} placeholder="e.g., 1.5" />
          </div>
          <div>
            <Label htmlFor="driving-hour">Driving Hour</Label>
            <Input id="driving-hour" value={drivingHour} onChange={(e) => setDrivingHour(e.target.value)} placeholder="e.g., 3" />
          </div>
          <div>
            <Label htmlFor="highest-altitude">Highest Altitude</Label>
            <Input id="highest-altitude" value={highestAltitude} onChange={(e) => setHighestAltitude(e.target.value)} placeholder="e.g., 4000m" />
          </div>
          <div>
            <Label htmlFor="trek-duration">Trek Duration</Label>
            <Input id="trek-duration" value={trekDuration} onChange={(e) => setTrekDuration(e.target.value)} placeholder="e.g., 6 hours" />
          </div>
          <div>
            <Label htmlFor="image">Image</Label>
            <Input id="image" type="file" onChange={(e) => e.target.files && setImage(e.target.files[0])} />
          </div>
          <Button type="submit" className="w-full">Add Itinerary</Button>
        </form>
      </CardContent>
    </Card>
  )
}