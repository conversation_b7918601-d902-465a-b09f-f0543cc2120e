'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Edit, Trash2 } from 'lucide-react'
import Image from "next/image"

// Updated interface to include all fields
interface ItineraryItem {
  id: number;
  day: string;
  title: string;
  details: string;
  image?: string;
}

interface ItineraryListProps {
  items: ItineraryItem[]; // This is now a required prop
}

export function ItineraryList({ items }: ItineraryListProps) {

  // If there are no items, you can display a message
  if (items.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Package Itinerary List</CardTitle>
        </CardHeader>
        <CardContent>
          <p>No itinerary items have been added yet.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Package Itinerary List</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Day</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Details</TableHead>
              <TableHead>Image</TableHead>
              <TableHead>Options</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.day}.</TableCell>
                <TableCell>{item.title}</TableCell>
                <TableCell className="max-w-xs">
                  <p className="text-sm text-gray-600">
                    {item.details}
                  </p>
                </TableCell>
                 <TableCell>
                  {item.image && (
                    <Image src={item.image} alt={item.title}
                    width={80} height={80} className="w-20 h-20 object-cover rounded-md" />
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" className="text-green-600 border-green-600 hover:bg-green-50 hover:text-green-700">
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="text-red-600 border-red-600 hover:bg-red-50 hover:text-red-700">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}